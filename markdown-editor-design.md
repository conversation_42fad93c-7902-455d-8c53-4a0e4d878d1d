# Markdown Editor with Table of Contents Design

## Overview

This document outlines the design for an enhanced markdown editing system that allows users to:
1. View a table of contents (TOC) for large markdown documents (300+ pages)
2. Click on TOC items to navigate to specific sections
3. Edit individual blocks/sections in-place
4. Save changes back to the backend

## Current System Analysis

### Frontend (React/TypeScript)
- **Editor**: TipTap editor with markdown support (`tiptap-markdown`)
- **Viewer**: Custom markdown viewer using `marked` library
- **Components**: `MarkdownPane`, `MarkdownEditor`, `MarkdownViewer`
- **AST Support**: Already has markdown AST handling via TipTap extensions

### Backend (Python/Flask)
- **Document Processing**: Existing markdown parsing utilities in `blitzy_platform_shared/document/utils.py`
- **Storage**: GCS-based file storage with presigned URLs
- **APIs**: Tech spec document endpoints for CRUD operations

## Data Models

### Frontend TypeScript Interfaces

```typescript
// Enhanced from existing src/types/markdown-editor.ts
export interface DocumentBlock {
    id: string;
    documentId: string;
    blockType: 'heading' | 'paragraph' | 'list' | 'code' | 'table' | 'blockquote' | 'image';
    headingLevel?: number; // 1-6 for headings
    headingText?: string;
    content: string;
    positionStart: number; // Character position in full document
    positionEnd: number;
    orderIndex: number; // Order within document
    parentBlockId?: string; // For nested structures
    createdAt: Date;
    updatedAt: Date;
    astNodeType: string; // AST node type from markdown parser
    astMetadata: Record<string, any>; // Additional AST data
}

export interface TOCItem {
    id: string;
    blockId: string;
    title: string;
    level: number; // 1-6 for heading levels
    anchor: string; // URL fragment identifier
    children: TOCItem[]; // Nested headings
    startPosition: number;
    endPosition: number;
}

export interface TableOfContents {
    id: string;
    documentId: string;
    tocStructure: TOCItem[];
    generatedAt: Date;
}

export interface MarkdownDocument {
    id: string;
    title: string;
    content: string;
    blocks: DocumentBlock[];
    tableOfContents: TableOfContents;
    version: number;
    lastModified: Date;
    checksum: string; // For conflict detection
}

export interface BlockEditRequest {
    blockId: string;
    newContent: string;
    version: number; // For optimistic locking
}

export interface BlockEditResponse {
    success: boolean;
    updatedBlock: DocumentBlock;
    newVersion: number;
    conflicts?: string[]; // If there are conflicts
}
```

### Backend Python Models

```python
# New models to add to common_models/models.py
from sqlalchemy import Column, String, Text, Integer, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

class MarkdownDocument(Base):
    __tablename__ = 'markdown_documents'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    version = Column(Integer, default=1)
    checksum = Column(String, nullable=False)  # MD5 hash for conflict detection
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    blocks = relationship("DocumentBlock", back_populates="document", cascade="all, delete-orphan")
    table_of_contents = relationship("TableOfContents", back_populates="document", uselist=False)

class DocumentBlock(Base):
    __tablename__ = 'document_blocks'
    
    id = Column(String, primary_key=True)
    document_id = Column(String, ForeignKey('markdown_documents.id'), nullable=False)
    block_type = Column(String, nullable=False)  # heading, paragraph, list, code, table, etc.
    heading_level = Column(Integer, nullable=True)
    heading_text = Column(String, nullable=True)
    content = Column(Text, nullable=False)
    position_start = Column(Integer, nullable=False)
    position_end = Column(Integer, nullable=False)
    order_index = Column(Integer, nullable=False)
    parent_block_id = Column(String, ForeignKey('document_blocks.id'), nullable=True)
    ast_node_type = Column(String, nullable=False)
    ast_metadata = Column(JSON, default={})
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    document = relationship("MarkdownDocument", back_populates="blocks")
    parent_block = relationship("DocumentBlock", remote_side=[id])

class TableOfContents(Base):
    __tablename__ = 'table_of_contents'
    
    id = Column(String, primary_key=True)
    document_id = Column(String, ForeignKey('markdown_documents.id'), nullable=False)
    toc_structure = Column(JSON, nullable=False)  # Serialized TOCItem tree
    generated_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    document = relationship("MarkdownDocument", back_populates="table_of_contents")
```

## API Design

### REST Endpoints

```python
# New routes in src/api/routes/markdown_documents.py

@markdown_bp.route("/<project_id>/documents/<document_id>", methods=["GET"])
def get_markdown_document(project_id: str, document_id: str):
    """Get full markdown document with blocks and TOC"""
    pass

@markdown_bp.route("/<project_id>/documents/<document_id>/toc", methods=["GET"])
def get_table_of_contents(project_id: str, document_id: str):
    """Get just the table of contents for navigation"""
    pass

@markdown_bp.route("/<project_id>/documents/<document_id>/blocks/<block_id>", methods=["GET"])
def get_document_block(project_id: str, document_id: str, block_id: str):
    """Get a specific block for editing"""
    pass

@markdown_bp.route("/<project_id>/documents/<document_id>/blocks/<block_id>", methods=["PUT"])
def update_document_block(project_id: str, document_id: str, block_id: str):
    """Update a specific block with optimistic locking"""
    pass

@markdown_bp.route("/<project_id>/documents/<document_id>/regenerate-toc", methods=["POST"])
def regenerate_table_of_contents(project_id: str, document_id: str):
    """Regenerate TOC after structural changes"""
    pass

@markdown_bp.route("/<project_id>/documents/<document_id>/parse", methods=["POST"])
def parse_markdown_to_blocks(project_id: str, document_id: str):
    """Parse full markdown content into blocks (for initial setup)"""
    pass
```

### Request/Response Models

```python
# API models in src/api/models/markdown_models.py

class GetDocumentResponse(BaseModel):
    document: MarkdownDocument
    blocks: List[DocumentBlock]
    tableOfContents: TableOfContents

class UpdateBlockRequest(BaseModel):
    content: str
    version: int

class UpdateBlockResponse(BaseModel):
    success: bool
    updatedBlock: DocumentBlock
    newVersion: int
    conflicts: Optional[List[str]] = None

class TOCResponse(BaseModel):
    tableOfContents: TableOfContents
    lastModified: datetime
```

## Architecture & Data Flow

```mermaid
graph TB
    subgraph "Frontend (React)"
        A[TOC Navigation Panel] --> B[Block Selection]
        B --> C[Inline Block Editor]
        C --> D[Save Block Changes]
        E[Document Viewer] --> F[Click to Edit Block]
        F --> C
    end
    
    subgraph "Backend (Python/Flask)"
        G[Markdown Parser Service] --> H[Block Management Service]
        H --> I[TOC Generator Service]
        I --> J[Database Layer]
        K[Conflict Resolution Service] --> H
    end
    
    subgraph "Database"
        L[(MarkdownDocument)]
        M[(DocumentBlock)]
        N[(TableOfContents)]
    end
    
    D --> |PUT /blocks/{id}| H
    A --> |GET /toc| I
    B --> |GET /blocks/{id}| H
    
    H --> L
    H --> M
    I --> N
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style H fill:#e8f5e8
    style I fill:#fff3e0
```

## Component Architecture

```mermaid
graph TB
    subgraph "Enhanced Markdown Editor Components"
        A[MarkdownDocumentContainer] --> B[TOCNavigationPanel]
        A --> C[MarkdownContentArea]
        
        B --> D[TOCTree]
        B --> E[TOCSearch]
        
        C --> F[MarkdownViewer]
        C --> G[InlineBlockEditor]
        
        F --> H[BlockHighlighter]
        F --> I[ClickToEditOverlay]
        
        G --> J[TipTapEditor]
        G --> K[BlockSaveControls]
    end
    
    subgraph "Services"
        L[MarkdownParserService]
        M[TOCGeneratorService]
        N[BlockEditService]
        O[ConflictResolutionService]
    end
    
    A --> L
    B --> M
    G --> N
    K --> O
    
    style A fill:#e3f2fd
    style B fill:#f1f8e9
    style G fill:#fce4ec
    style L fill:#fff8e1

## Implementation Strategy

### Phase 1: Backend Foundation

#### 1. Markdown AST Parser Enhancement
Extend existing `blitzy_platform_shared/document/utils.py` with block-level parsing:

```python
# Enhanced markdown parser using python-markdown with extensions
import markdown
from markdown.extensions import toc, codehilite, tables
from markdown.treeprocessors import Treeprocessor
from markdown.extensions import Extension

class BlockExtractorTreeprocessor(Treeprocessor):
    """Extract document blocks from markdown AST"""

    def run(self, root):
        blocks = []
        position = 0

        for element in root.iter():
            block = self._element_to_block(element, position)
            if block:
                blocks.append(block)
                position = block.position_end

        return blocks

    def _element_to_block(self, element, start_pos):
        """Convert XML element to DocumentBlock"""
        block_type = self._get_block_type(element.tag)
        content = self._extract_content(element)

        return DocumentBlock(
            block_type=block_type,
            content=content,
            position_start=start_pos,
            position_end=start_pos + len(content),
            ast_node_type=element.tag,
            ast_metadata=dict(element.attrib)
        )

def parse_markdown_to_blocks(markdown_content: str) -> List[DocumentBlock]:
    """Parse markdown content into structured blocks"""
    md = markdown.Markdown(extensions=[
        'toc',
        'codehilite',
        'tables',
        'fenced_code',
        BlockExtractorExtension()
    ])

    html = md.convert(markdown_content)
    return md.treeprocessors['block_extractor'].blocks
```

#### 2. Database Schema Migration
```sql
-- Migration script for new tables
CREATE TABLE markdown_documents (
    id VARCHAR(255) PRIMARY KEY,
    project_id VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    version INTEGER DEFAULT 1,
    checksum VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

CREATE TABLE document_blocks (
    id VARCHAR(255) PRIMARY KEY,
    document_id VARCHAR(255) NOT NULL,
    block_type VARCHAR(50) NOT NULL,
    heading_level INTEGER NULL,
    heading_text VARCHAR(500) NULL,
    content TEXT NOT NULL,
    position_start INTEGER NOT NULL,
    position_end INTEGER NOT NULL,
    order_index INTEGER NOT NULL,
    parent_block_id VARCHAR(255) NULL,
    ast_node_type VARCHAR(100) NOT NULL,
    ast_metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES markdown_documents(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_block_id) REFERENCES document_blocks(id)
);

CREATE TABLE table_of_contents (
    id VARCHAR(255) PRIMARY KEY,
    document_id VARCHAR(255) NOT NULL,
    toc_structure JSON NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES markdown_documents(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX idx_document_blocks_document_id ON document_blocks(document_id);
CREATE INDEX idx_document_blocks_order ON document_blocks(document_id, order_index);
CREATE INDEX idx_document_blocks_position ON document_blocks(document_id, position_start, position_end);
```

### Phase 2: Frontend Components

#### 1. Enhanced TOC Navigation Component
```typescript
// src/components/markdown/TOCNavigationPanel.tsx
interface TOCNavigationPanelProps {
    tableOfContents: TableOfContents;
    currentBlockId?: string;
    onBlockSelect: (blockId: string) => void;
    onBlockEdit: (blockId: string) => void;
}

export const TOCNavigationPanel: React.FC<TOCNavigationPanelProps> = ({
    tableOfContents,
    currentBlockId,
    onBlockSelect,
    onBlockEdit
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

    const filteredTOC = useMemo(() => {
        return filterTOCItems(tableOfContents.tocStructure, searchTerm);
    }, [tableOfContents, searchTerm]);

    return (
        <div className="toc-navigation-panel">
            <div className="toc-search">
                <input
                    type="text"
                    placeholder="Search sections..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="toc-search-input"
                />
            </div>

            <div className="toc-tree">
                {filteredTOC.map(item => (
                    <TOCTreeItem
                        key={item.id}
                        item={item}
                        currentBlockId={currentBlockId}
                        expandedItems={expandedItems}
                        onToggleExpand={toggleExpand}
                        onBlockSelect={onBlockSelect}
                        onBlockEdit={onBlockEdit}
                    />
                ))}
            </div>
        </div>
    );
};
```

#### 2. Inline Block Editor Component
```typescript
// src/components/markdown/InlineBlockEditor.tsx
interface InlineBlockEditorProps {
    block: DocumentBlock;
    onSave: (blockId: string, content: string) => Promise<void>;
    onCancel: () => void;
    isLoading?: boolean;
}

export const InlineBlockEditor: React.FC<InlineBlockEditorProps> = ({
    block,
    onSave,
    onCancel,
    isLoading = false
}) => {
    const [content, setContent] = useState(block.content);
    const [hasChanges, setHasChanges] = useState(false);
    const editorRef = useRef<IEditor>(null);

    const handleSave = async () => {
        if (hasChanges && content !== block.content) {
            await onSave(block.id, content);
        }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            onCancel();
        } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            handleSave();
        }
    };

    return (
        <div className="inline-block-editor">
            <div className="editor-toolbar">
                <span className="block-type-indicator">
                    {block.blockType} {block.headingLevel && `H${block.headingLevel}`}
                </span>
                <div className="editor-actions">
                    <button
                        onClick={handleSave}
                        disabled={!hasChanges || isLoading}
                        className="save-btn"
                    >
                        {isLoading ? 'Saving...' : 'Save'}
                    </button>
                    <button onClick={onCancel} className="cancel-btn">
                        Cancel
                    </button>
                </div>
            </div>

            <TiptapEditor
                ref={editorRef}
                markdown={content}
                onChange={setContent}
                onContentChange={setHasChanges}
                editable={true}
                docType="markdown"
                className="inline-editor"
            />
        </div>
    );
};
```

### Phase 3: Service Layer Implementation

#### 1. Block Management Service
```python
# src/service/markdown_service.py
from typing import List, Optional, Tuple
import hashlib
import json
from sqlalchemy.orm import Session
from common_models.models import MarkdownDocument, DocumentBlock, TableOfContents

class MarkdownDocumentService:

    def __init__(self, db_session: Session):
        self.db = db_session

    def parse_document_to_blocks(self, document_id: str, content: str) -> List[DocumentBlock]:
        """Parse markdown content and create/update blocks"""
        blocks = parse_markdown_to_blocks(content)

        # Clear existing blocks
        self.db.query(DocumentBlock).filter_by(document_id=document_id).delete()

        # Create new blocks
        for i, block_data in enumerate(blocks):
            block = DocumentBlock(
                id=generate_uuid(),
                document_id=document_id,
                order_index=i,
                **block_data
            )
            self.db.add(block)

        self.db.commit()
        return blocks

    def update_block(self, block_id: str, new_content: str, version: int) -> Tuple[bool, DocumentBlock, Optional[List[str]]]:
        """Update a single block with optimistic locking"""
        block = self.db.query(DocumentBlock).filter_by(id=block_id).first()
        if not block:
            raise BlockNotFoundError(f"Block {block_id} not found")

        # Check document version for conflicts
        document = self.db.query(MarkdownDocument).filter_by(id=block.document_id).first()
        if document.version != version:
            conflicts = self._detect_conflicts(block, new_content, document.version)
            return False, block, conflicts

        # Update block content
        old_content = block.content
        block.content = new_content
        block.updated_at = datetime.utcnow()

        # Update document version and checksum
        document.version += 1
        document.content = self._rebuild_document_content(document.id)
        document.checksum = self._calculate_checksum(document.content)
        document.updated_at = datetime.utcnow()

        self.db.commit()

        # Regenerate TOC if heading changed
        if block.block_type == 'heading':
            self._regenerate_toc(document.id)

        return True, block, None

    def _rebuild_document_content(self, document_id: str) -> str:
        """Rebuild full document content from blocks"""
        blocks = self.db.query(DocumentBlock)\
            .filter_by(document_id=document_id)\
            .order_by(DocumentBlock.order_index)\
            .all()

        return '\n\n'.join(block.content for block in blocks)

    def _calculate_checksum(self, content: str) -> str:
        """Calculate MD5 checksum for conflict detection"""
        return hashlib.md5(content.encode()).hexdigest()

class TOCGeneratorService:

    def __init__(self, db_session: Session):
        self.db = db_session

    def generate_toc(self, document_id: str) -> TableOfContents:
        """Generate table of contents from document blocks"""
        heading_blocks = self.db.query(DocumentBlock)\
            .filter_by(document_id=document_id, block_type='heading')\
            .order_by(DocumentBlock.order_index)\
            .all()

        toc_structure = self._build_toc_tree(heading_blocks)

        # Update or create TOC record
        toc = self.db.query(TableOfContents).filter_by(document_id=document_id).first()
        if not toc:
            toc = TableOfContents(
                id=generate_uuid(),
                document_id=document_id
            )
            self.db.add(toc)

        toc.toc_structure = toc_structure
        toc.generated_at = datetime.utcnow()

        self.db.commit()
        return toc

    def _build_toc_tree(self, heading_blocks: List[DocumentBlock]) -> List[dict]:
        """Build hierarchical TOC structure"""
        toc_items = []
        stack = []  # Stack to track parent headings

        for block in heading_blocks:
            toc_item = {
                'id': generate_uuid(),
                'blockId': block.id,
                'title': block.heading_text or block.content[:50],
                'level': block.heading_level,
                'anchor': self._generate_anchor(block.heading_text),
                'startPosition': block.position_start,
                'endPosition': block.position_end,
                'children': []
            }

            # Find correct parent level
            while stack and stack[-1]['level'] >= block.heading_level:
                stack.pop()

            if stack:
                stack[-1]['children'].append(toc_item)
            else:
                toc_items.append(toc_item)

            stack.append(toc_item)

        return toc_items

    def _generate_anchor(self, heading_text: str) -> str:
        """Generate URL-safe anchor from heading text"""
        if not heading_text:
            return ''

        # Convert to lowercase, replace spaces with hyphens, remove special chars
        anchor = re.sub(r'[^\w\s-]', '', heading_text.lower())
        anchor = re.sub(r'[-\s]+', '-', anchor)
        return anchor.strip('-')
```

### Phase 4: Frontend Integration

#### 1. Main Document Container
```typescript
// src/components/markdown/MarkdownDocumentContainer.tsx
export const MarkdownDocumentContainer: React.FC<{documentId: string}> = ({documentId}) => {
    const [document, setDocument] = useState<MarkdownDocument | null>(null);
    const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
    const [editingBlockId, setEditingBlockId] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const markdownService = useMarkdownService();

    useEffect(() => {
        loadDocument();
    }, [documentId]);

    const loadDocument = async () => {
        setIsLoading(true);
        try {
            const doc = await markdownService.getDocument(documentId);
            setDocument(doc);
        } catch (error) {
            console.error('Failed to load document:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleBlockEdit = async (blockId: string, newContent: string) => {
        if (!document) return;

        try {
            const result = await markdownService.updateBlock(blockId, newContent, document.version);

            if (result.success) {
                // Update local state
                setDocument(prev => ({
                    ...prev!,
                    version: result.newVersion,
                    blocks: prev!.blocks.map(block =>
                        block.id === blockId ? result.updatedBlock : block
                    )
                }));
                setEditingBlockId(null);
            } else {
                // Handle conflicts
                handleEditConflicts(result.conflicts);
            }
        } catch (error) {
            console.error('Failed to update block:', error);
        }
    };

    const handleTOCNavigation = (blockId: string) => {
        setSelectedBlockId(blockId);
        // Scroll to block
        const blockElement = document.getElementById(`block-${blockId}`);
        blockElement?.scrollIntoView({behavior: 'smooth', block: 'center'});
    };

    if (isLoading || !document) {
        return <div>Loading document...</div>;
    }

    return (
        <div className="markdown-document-container">
            <div className="toc-sidebar">
                <TOCNavigationPanel
                    tableOfContents={document.tableOfContents}
                    currentBlockId={selectedBlockId}
                    onBlockSelect={handleTOCNavigation}
                    onBlockEdit={setEditingBlockId}
                />
            </div>

            <div className="document-content">
                <MarkdownDocumentViewer
                    document={document}
                    selectedBlockId={selectedBlockId}
                    editingBlockId={editingBlockId}
                    onBlockClick={setSelectedBlockId}
                    onBlockEdit={setEditingBlockId}
                    onBlockSave={handleBlockEdit}
                    onEditCancel={() => setEditingBlockId(null)}
                />
            </div>
        </div>
    );
};
```

## Technical Considerations

### Performance Optimization

#### 1. Large Document Handling
```typescript
// Virtual scrolling for large documents
import { Virtuoso } from 'react-virtuoso';

const VirtualizedDocumentViewer: React.FC = ({blocks}) => {
    return (
        <Virtuoso
            data={blocks}
            itemContent={(index, block) => (
                <DocumentBlockComponent
                    key={block.id}
                    block={block}
                    onEdit={handleBlockEdit}
                />
            )}
            overscan={5}
            increaseViewportBy={200}
        />
    );
};
```

#### 2. Debounced Auto-save
```typescript
const useAutoSave = (blockId: string, content: string, delay = 2000) => {
    const debouncedSave = useMemo(
        () => debounce(async (id: string, content: string) => {
            await markdownService.updateBlock(id, content);
        }, delay),
        [delay]
    );

    useEffect(() => {
        if (content) {
            debouncedSave(blockId, content);
        }
    }, [blockId, content, debouncedSave]);
};
```

#### 3. Caching Strategy
```python
# Redis caching for frequently accessed documents
from redis import Redis
import json

class CachedMarkdownService:
    def __init__(self, redis_client: Redis, db_service: MarkdownDocumentService):
        self.redis = redis_client
        self.db_service = db_service

    def get_document(self, document_id: str) -> MarkdownDocument:
        cache_key = f"markdown_doc:{document_id}"
        cached = self.redis.get(cache_key)

        if cached:
            return MarkdownDocument.parse_raw(cached)

        document = self.db_service.get_document(document_id)
        self.redis.setex(cache_key, 3600, document.json())  # 1 hour cache
        return document

    def invalidate_cache(self, document_id: str):
        self.redis.delete(f"markdown_doc:{document_id}")
```

### Security Considerations

#### 1. Content Sanitization
```python
import bleach
from markdown import markdown

def sanitize_markdown_content(content: str) -> str:
    """Sanitize markdown content to prevent XSS"""
    # Convert markdown to HTML first
    html = markdown(content)

    # Sanitize HTML
    allowed_tags = [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 'code',
        'pre', 'blockquote', 'ul', 'ol', 'li',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'a', 'img'
    ]

    allowed_attributes = {
        'a': ['href', 'title'],
        'img': ['src', 'alt', 'title'],
        'code': ['class'],
        'pre': ['class']
    }

    return bleach.clean(html, tags=allowed_tags, attributes=allowed_attributes)
```

#### 2. Access Control
```python
def check_document_access(user_id: str, document_id: str, action: str) -> bool:
    """Check if user has permission to perform action on document"""
    document = get_document(document_id)
    project = get_project(document.project_id)

    user_role = get_user_project_role(user_id, project.id)

    permissions = {
        'read': ['owner', 'admin', 'editor', 'viewer'],
        'edit': ['owner', 'admin', 'editor'],
        'delete': ['owner', 'admin']
    }

    return user_role in permissions.get(action, [])
```

### Error Handling & Conflict Resolution

#### 1. Optimistic Locking
```typescript
interface ConflictResolution {
    strategy: 'merge' | 'overwrite' | 'cancel';
    mergedContent?: string;
}

const ConflictResolutionDialog: React.FC<{
    conflicts: string[];
    localContent: string;
    serverContent: string;
    onResolve: (resolution: ConflictResolution) => void;
}> = ({conflicts, localContent, serverContent, onResolve}) => {
    const [strategy, setStrategy] = useState<'merge' | 'overwrite' | 'cancel'>('merge');

    const handleMerge = () => {
        // Implement 3-way merge logic
        const mergedContent = merge3Way(localContent, serverContent, conflicts);
        onResolve({strategy: 'merge', mergedContent});
    };

    return (
        <Dialog>
            <DialogContent>
                <h3>Content Conflict Detected</h3>
                <p>The content has been modified by another user. How would you like to resolve this?</p>

                <div className="conflict-options">
                    <button onClick={() => setStrategy('merge')}>
                        Merge Changes
                    </button>
                    <button onClick={() => setStrategy('overwrite')}>
                        Overwrite Server Version
                    </button>
                    <button onClick={() => setStrategy('cancel')}>
                        Cancel Edit
                    </button>
                </div>

                <div className="content-diff">
                    <DiffViewer
                        oldValue={serverContent}
                        newValue={localContent}
                        splitView={true}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
};
```

## Implementation Timeline

### Week 1-2: Backend Foundation
- [ ] Database schema design and migration
- [ ] Enhanced markdown parser with block extraction
- [ ] Basic CRUD APIs for documents and blocks
- [ ] TOC generation service

### Week 3-4: Core Frontend Components
- [ ] TOC navigation panel with search
- [ ] Block-level editing interface
- [ ] Document viewer with click-to-edit
- [ ] Basic conflict resolution

### Week 5-6: Integration & Optimization
- [ ] Frontend-backend integration
- [ ] Performance optimization (virtualization, caching)
- [ ] Auto-save functionality
- [ ] Error handling and user feedback

### Week 7-8: Advanced Features & Polish
- [ ] Advanced conflict resolution
- [ ] Real-time collaboration indicators
- [ ] Accessibility improvements
- [ ] Comprehensive testing

## Success Metrics

1. **Performance**: Document loading < 2s for 300+ page documents
2. **Usability**: TOC navigation response time < 100ms
3. **Reliability**: 99.9% uptime for block editing operations
4. **User Experience**: < 3 clicks to edit any section from TOC
5. **Data Integrity**: Zero data loss during concurrent editing

## Future Enhancements

1. **Real-time Collaboration**: WebSocket-based live editing
2. **Version History**: Git-like versioning with branch/merge
3. **Comments & Annotations**: Inline commenting system
4. **Export Options**: PDF, Word, HTML export with TOC
5. **AI Integration**: Smart content suggestions and auto-completion
```
```
```
